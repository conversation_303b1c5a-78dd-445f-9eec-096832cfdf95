import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/ai_writing_controller.dart';
import '../controllers/api_config_controller.dart';
import '../controllers/novel_agent_controller.dart';
import '../models/novel.dart';
import '../models/writing_style_package.dart';

/// AI写作面板组件
class AIWritingPanel extends StatefulWidget {
  final Novel novel;
  final double width;
  final bool isMobile;

  const AIWritingPanel({
    super.key,
    required this.novel,
    this.width = 400,
    this.isMobile = false,
  });

  @override
  State<AIWritingPanel> createState() => _AIWritingPanelState();
}

class _AIWritingPanelState extends State<AIWritingPanel> {
  final AIWritingController _controller = Get.put(AIWritingController());
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _initializeController();
  }

  void _initializeController() async {
    try {
      await _controller.setCurrentNovel(widget.novel);
    } catch (e) {
      print('[AIWritingPanel] 初始化控制器失败: $e');
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        border: Border(
          left: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: _buildContent(),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withValues(alpha: 0.05),
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.edit_note,
            color: Theme.of(context).primaryColor,
            size: 18,
          ),
          const SizedBox(width: 6),
          Expanded(
            child: Text(
              'AI写作助手',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14,
                color: Theme.of(context).primaryColor,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
          // 模型选择按钮
          PopupMenuButton<String>(
            icon: Icon(
              Icons.settings,
              size: 16,
              color: Theme.of(context).primaryColor,
            ),
            tooltip: '模型设置',
            onSelected: (modelId) {
              _controller.switchModel(modelId);
            },
            itemBuilder: (context) {
              // 获取可用的模型列表
              final apiController = Get.find<ApiConfigController>();
              final models = apiController.models;
              final currentModelId = _controller.selectedModelId.value;

              return [
                PopupMenuItem(
                  enabled: false,
                  child: Text(
                    '当前模型: $currentModelId',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ),
                const PopupMenuDivider(),
                ...models.map((model) => PopupMenuItem<String>(
                      value: model.name,
                      child: Row(
                        children: [
                          Icon(
                            model.name == currentModelId
                                ? Icons.radio_button_checked
                                : Icons.radio_button_unchecked,
                            size: 16,
                            color: model.name == currentModelId
                                ? Theme.of(context).primaryColor
                                : Colors.grey,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  model.name,
                                  style: const TextStyle(
                                      fontWeight: FontWeight.w500),
                                ),
                                Text(
                                  '${model.apiFormat} • ${model.model}',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    )),
              ];
            },
          ),
        ],
      ),
    );
  }

  /// 构建主要内容
  Widget _buildContent() {
    return Obx(() {
      if (_controller.currentNovel.value == null) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      }

      return SingleChildScrollView(
        controller: _scrollController,
        padding: EdgeInsets.all(widget.isMobile ? 12 : 16),
        physics: const BouncingScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildChapterSelector(),
            SizedBox(height: widget.isMobile ? 12 : 16),
            _buildOutlineEditor(),
            SizedBox(height: widget.isMobile ? 12 : 16),
            _buildKnowledgeBaseSection(),
            SizedBox(height: widget.isMobile ? 12 : 16),
            _buildStylePackageSection(),
            SizedBox(height: widget.isMobile ? 12 : 16),
            _buildCharacterSection(),
            SizedBox(height: widget.isMobile ? 12 : 16),
            _buildContextSection(),
            SizedBox(height: widget.isMobile ? 20 : 24),
            _buildGenerateButton(),
            SizedBox(height: widget.isMobile ? 20 : 24), // 底部间距
          ],
        ),
      );
    });
  }

  /// 构建章节选择器
  Widget _buildChapterSelector() {
    return Obx(() {
      final novel = _controller.currentNovel.value;
      if (novel == null || novel.chapters.isEmpty) {
        return const SizedBox.shrink();
      }

      return Card(
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '当前章节',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).primaryColor,
                ),
              ),
              const SizedBox(height: 8),
              DropdownButton<int>(
                value: _controller.currentChapterIndex.value,
                isExpanded: true,
                items: novel.chapters.asMap().entries.map((entry) {
                  final index = entry.key;
                  final chapter = entry.value;
                  return DropdownMenuItem<int>(
                    value: index,
                    child: Text('第${chapter.number}章 ${chapter.title}'),
                  );
                }).toList(),
                onChanged: (index) {
                  if (index != null) {
                    _controller.setCurrentChapter(index);
                  }
                },
              ),
            ],
          ),
        ),
      );
    });
  }

  /// 构建细纲编辑器
  Widget _buildOutlineEditor() {
    return Obx(() => Card(
          child: Padding(
            padding: EdgeInsets.all(widget.isMobile ? 8 : 12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        '章节细纲',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: widget.isMobile ? 14 : 16,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    ),
                    if (_controller.isEditingOutline.value) ...[
                      TextButton(
                        onPressed: _controller.saveOutlineEdit,
                        style: TextButton.styleFrom(
                          padding: EdgeInsets.symmetric(
                            horizontal: widget.isMobile ? 8 : 12,
                            vertical: widget.isMobile ? 4 : 8,
                          ),
                        ),
                        child: Text(
                          '保存',
                          style: TextStyle(fontSize: widget.isMobile ? 12 : 14),
                        ),
                      ),
                      TextButton(
                        onPressed: _controller.cancelOutlineEdit,
                        style: TextButton.styleFrom(
                          padding: EdgeInsets.symmetric(
                            horizontal: widget.isMobile ? 8 : 12,
                            vertical: widget.isMobile ? 4 : 8,
                          ),
                        ),
                        child: Text(
                          '取消',
                          style: TextStyle(fontSize: widget.isMobile ? 12 : 14),
                        ),
                      ),
                    ] else
                      TextButton(
                        onPressed: _controller.startEditingOutline,
                        style: TextButton.styleFrom(
                          padding: EdgeInsets.symmetric(
                            horizontal: widget.isMobile ? 8 : 12,
                            vertical: widget.isMobile ? 4 : 8,
                          ),
                        ),
                        child: Text(
                          '编辑',
                          style: TextStyle(fontSize: widget.isMobile ? 12 : 14),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 8),
                ConstrainedBox(
                  constraints: BoxConstraints(
                    maxHeight: widget.isMobile ? 200 : 250,
                  ),
                  child: TextField(
                    controller: _controller.outlineController,
                    maxLines: null,
                    minLines: widget.isMobile ? 6 : 8,
                    readOnly: !_controller.isEditingOutline.value,
                    style: TextStyle(fontSize: widget.isMobile ? 12 : 14),
                    decoration: InputDecoration(
                      hintText: '请输入章节细纲...',
                      hintStyle: TextStyle(fontSize: widget.isMobile ? 12 : 14),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: EdgeInsets.all(widget.isMobile ? 8 : 12),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ));
  }

  /// 构建知识库选择部分
  Widget _buildKnowledgeBaseSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    '知识库',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ),
                Obx(() => Switch(
                      value: _controller.useKnowledgeBase.value,
                      onChanged: _controller.toggleKnowledgeBase,
                    )),
              ],
            ),
            Obx(() {
              if (!_controller.useKnowledgeBase.value) {
                return const SizedBox.shrink();
              }

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: Obx(() => Text(
                              _controller.selectedKnowledgeIds.isEmpty
                                  ? '未选择知识库文档'
                                  : '已选择 ${_controller.selectedKnowledgeIds.length} 个文档',
                              style: TextStyle(
                                  fontSize: widget.isMobile ? 12 : 14),
                            )),
                      ),
                      ElevatedButton.icon(
                        onPressed: () => _showKnowledgeSelectionDialog(),
                        icon: Icon(
                          Icons.library_books,
                          size: widget.isMobile ? 14 : 16,
                        ),
                        label: Text(
                          '选择文档',
                          style: TextStyle(fontSize: widget.isMobile ? 11 : 12),
                        ),
                        style: ElevatedButton.styleFrom(
                          padding: EdgeInsets.symmetric(
                            horizontal: widget.isMobile ? 8 : 12,
                            vertical: widget.isMobile ? 4 : 8,
                          ),
                        ),
                      ),
                    ],
                  ),
                  if (_controller.selectedKnowledgeIds.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    Container(
                      padding: EdgeInsets.all(widget.isMobile ? 6 : 8),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade50,
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: Wrap(
                        spacing: 4,
                        runSpacing: 4,
                        children: _controller
                            .getSelectedKnowledgeDocuments()
                            .map((doc) {
                          return Chip(
                            label: Text(
                              doc.title,
                              style: TextStyle(
                                  fontSize: widget.isMobile ? 10 : 12),
                            ),
                            deleteIcon: Icon(
                              Icons.close,
                              size: widget.isMobile ? 14 : 16,
                            ),
                            onDeleted: () =>
                                _controller.toggleKnowledgeDocument(doc.id),
                            visualDensity: VisualDensity.compact,
                          );
                        }).toList(),
                      ),
                    ),
                  ],
                ],
              );
            }),
          ],
        ),
      ),
    );
  }

  /// 构建文风包选择部分
  Widget _buildStylePackageSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    '文风包',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ),
                Obx(() => Switch(
                      value: _controller.useStylePackage.value,
                      onChanged: _controller.toggleStylePackage,
                    )),
              ],
            ),
            Obx(() {
              if (!_controller.useStylePackage.value) {
                return const SizedBox.shrink();
              }

              final packages = _controller.getAvailableStylePackages();
              if (packages.isEmpty) {
                return const Padding(
                  padding: EdgeInsets.only(top: 8),
                  child: Text('暂无可用的文风包'),
                );
              }

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 8),
                  ElevatedButton.icon(
                    onPressed: () => _showStylePackageDialog(),
                    icon: const Icon(Icons.style, size: 16),
                    label: Text(
                      _controller.selectedStylePackage.value?.name ?? '选择文风包',
                      style: const TextStyle(fontSize: 12),
                    ),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      minimumSize: const Size(double.infinity, 36),
                    ),
                  ),
                  if (_controller.selectedStylePackage.value != null) ...[
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade50,
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '已选择: ${_controller.selectedStylePackage.value!.name}',
                            style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          if (_controller.selectedStylePackage.value!.author
                              .isNotEmpty) ...[
                            const SizedBox(height: 4),
                            Text(
                              '作者: ${_controller.selectedStylePackage.value!.author}',
                              style: TextStyle(
                                fontSize: 11,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                          const SizedBox(height: 4),
                          Text(
                            _controller.selectedStylePackage.value!.description,
                            style: TextStyle(
                              fontSize: 11,
                              color: Colors.grey[700],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              );
            }),
          ],
        ),
      ),
    );
  }

  /// 构建角色选择部分
  Widget _buildCharacterSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '角色设置',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(height: 8),
            Obx(() => CheckboxListTile(
                  title: const Text('允许AI创造新角色'),
                  value: _controller.allowCreateNewCharacters.value,
                  onChanged: (value) {
                    _controller.toggleAllowCreateNewCharacters(value ?? true);
                  },
                  dense: true,
                )),
            const SizedBox(height: 8),
            // 角色类型选择
            Row(
              children: [
                Expanded(
                  child: Text(
                    '角色类型:',
                    style: TextStyle(fontSize: widget.isMobile ? 12 : 14),
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () => _showCharacterTypeSelectionDialog(),
                  icon: Icon(
                    Icons.category,
                    size: widget.isMobile ? 14 : 16,
                  ),
                  label: Text(
                    '选择类型',
                    style: TextStyle(fontSize: widget.isMobile ? 11 : 12),
                  ),
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.symmetric(
                      horizontal: widget.isMobile ? 8 : 12,
                      vertical: widget.isMobile ? 4 : 8,
                    ),
                  ),
                ),
              ],
            ),
            Obx(() {
              if (_controller.selectedCharacterTypeIds.isEmpty) {
                return const SizedBox.shrink();
              }

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 8),
                  Container(
                    padding: EdgeInsets.all(widget.isMobile ? 6 : 8),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(color: Colors.blue.shade300),
                    ),
                    child: Wrap(
                      spacing: 4,
                      runSpacing: 4,
                      children:
                          _controller.getSelectedCharacterTypes().map((type) {
                        return Chip(
                          label: Text(
                            type.name,
                            style:
                                TextStyle(fontSize: widget.isMobile ? 10 : 12),
                          ),
                          deleteIcon: Icon(
                            Icons.close,
                            size: widget.isMobile ? 14 : 16,
                          ),
                          onDeleted: () =>
                              _controller.toggleCharacterType(type.id),
                          visualDensity: VisualDensity.compact,
                        );
                      }).toList(),
                    ),
                  ),
                ],
              );
            }),
            const SizedBox(height: 8),
            // 角色卡片选择
            Row(
              children: [
                Expanded(
                  child: Obx(() => Text(
                        _controller.selectedCharacterIds.isEmpty
                            ? '未选择角色卡片'
                            : '已选择 ${_controller.selectedCharacterIds.length} 个角色',
                        style: TextStyle(fontSize: widget.isMobile ? 12 : 14),
                      )),
                ),
                ElevatedButton.icon(
                  onPressed: () => _showCharacterCardSelectionDialog(),
                  icon: Icon(
                    Icons.person,
                    size: widget.isMobile ? 14 : 16,
                  ),
                  label: Text(
                    '选择角色',
                    style: TextStyle(fontSize: widget.isMobile ? 11 : 12),
                  ),
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.symmetric(
                      horizontal: widget.isMobile ? 8 : 12,
                      vertical: widget.isMobile ? 4 : 8,
                    ),
                  ),
                ),
              ],
            ),
            Obx(() {
              if (_controller.selectedCharacterIds.isEmpty) {
                return const SizedBox.shrink();
              }

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 8),
                  Container(
                    padding: EdgeInsets.all(widget.isMobile ? 6 : 8),
                    decoration: BoxDecoration(
                      color: Colors.green.shade50,
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(color: Colors.green.shade300),
                    ),
                    child: Wrap(
                      spacing: 4,
                      runSpacing: 4,
                      children:
                          _controller.getSelectedCharacterCards().map((card) {
                        return Chip(
                          label: Text(
                            card.name,
                            style:
                                TextStyle(fontSize: widget.isMobile ? 10 : 12),
                          ),
                          deleteIcon: Icon(
                            Icons.close,
                            size: widget.isMobile ? 14 : 16,
                          ),
                          onDeleted: () =>
                              _controller.toggleCharacterCard(card.id),
                          visualDensity: VisualDensity.compact,
                        );
                      }).toList(),
                    ),
                  ),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }

  /// 构建上下文关联部分
  Widget _buildContextSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    '上下文关联',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ),
                Obx(() => Switch(
                      value: _controller.useContextAssociation.value,
                      onChanged: _controller.toggleContextAssociation,
                    )),
              ],
            ),
            Obx(() {
              if (!_controller.useContextAssociation.value) {
                return const SizedBox.shrink();
              }

              final relatedChapters = _controller.getRelatedChapters();
              if (relatedChapters.isEmpty) {
                return const Padding(
                  padding: EdgeInsets.only(top: 8),
                  child: Text('暂无相关章节'),
                );
              }

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 8),
                  const Text('相关章节:'),
                  ...relatedChapters.map((chapter) => Padding(
                        padding: const EdgeInsets.only(left: 16, top: 4),
                        child: Text('• 第${chapter.number}章 ${chapter.title}'),
                      )),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }

  /// 构建生成按钮
  Widget _buildGenerateButton() {
    return Obx(() => SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _controller.isGenerating.value
                ? null
                : () {
                    _startAIGeneration();
                  },
            icon: _controller.isGenerating.value
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.auto_awesome),
            label: Text(_controller.isGenerating.value ? '生成中...' : '开始AI写作'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ));
  }

  /// 显示知识库选择对话框
  void _showKnowledgeSelectionDialog() {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600 || screenSize.height < 700;

    Get.dialog(
      AlertDialog(
        title: Text(
          '选择知识库文档',
          style: TextStyle(fontSize: isSmallScreen ? 16 : 18),
        ),
        content: SizedBox(
          width: isSmallScreen ? screenSize.width * 0.9 : 500,
          height: isSmallScreen ? screenSize.height * 0.6 : 400,
          child: Obx(() {
            final documents = _controller.getAvailableKnowledgeDocuments();
            if (documents.isEmpty) {
              return const Center(
                child: Text('暂无可用的知识库文档'),
              );
            }

            return ListView.builder(
              itemCount: documents.length,
              itemBuilder: (context, index) {
                final doc = documents[index];
                final isSelected =
                    _controller.selectedKnowledgeIds.contains(doc.id);

                return CheckboxListTile(
                  title: Text(
                    doc.title,
                    style: TextStyle(fontSize: isSmallScreen ? 12 : 14),
                  ),
                  subtitle: Text(
                    doc.category,
                    style: TextStyle(fontSize: isSmallScreen ? 10 : 12),
                  ),
                  value: isSelected,
                  onChanged: (selected) {
                    _controller.toggleKnowledgeDocument(doc.id);
                  },
                  dense: true,
                  visualDensity: isSmallScreen
                      ? VisualDensity.compact
                      : VisualDensity.standard,
                );
              },
            );
          }),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              '完成',
              style: TextStyle(fontSize: isSmallScreen ? 12 : 14),
            ),
          ),
        ],
      ),
    );
  }

  /// 显示角色类型选择对话框
  void _showCharacterTypeSelectionDialog() {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600 || screenSize.height < 700;

    Get.dialog(
      AlertDialog(
        title: Text(
          '选择角色类型',
          style: TextStyle(fontSize: isSmallScreen ? 16 : 18),
        ),
        content: SizedBox(
          width: isSmallScreen ? screenSize.width * 0.9 : 500,
          height: isSmallScreen ? screenSize.height * 0.6 : 400,
          child: Obx(() {
            final types = _controller.getAvailableCharacterTypes();
            if (types.isEmpty) {
              return const Center(
                child: Text('暂无可用的角色类型'),
              );
            }

            return ListView.builder(
              itemCount: types.length,
              itemBuilder: (context, index) {
                final type = types[index];
                final isSelected =
                    _controller.selectedCharacterTypeIds.contains(type.id);

                return CheckboxListTile(
                  title: Text(
                    type.name,
                    style: TextStyle(fontSize: isSmallScreen ? 12 : 14),
                  ),
                  subtitle: Text(
                    type.description,
                    style: TextStyle(fontSize: isSmallScreen ? 10 : 12),
                  ),
                  value: isSelected,
                  onChanged: (selected) {
                    _controller.toggleCharacterType(type.id);
                  },
                  dense: true,
                  visualDensity: isSmallScreen
                      ? VisualDensity.compact
                      : VisualDensity.standard,
                );
              },
            );
          }),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              '完成',
              style: TextStyle(fontSize: isSmallScreen ? 12 : 14),
            ),
          ),
        ],
      ),
    );
  }

  /// 显示角色卡片选择对话框
  void _showCharacterCardSelectionDialog() {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600 || screenSize.height < 700;

    Get.dialog(
      AlertDialog(
        title: Text(
          '选择角色卡片',
          style: TextStyle(fontSize: isSmallScreen ? 16 : 18),
        ),
        content: SizedBox(
          width: isSmallScreen ? screenSize.width * 0.9 : 500,
          height: isSmallScreen ? screenSize.height * 0.6 : 400,
          child: Obx(() {
            final cards = _controller.getFilteredCharacterCards();
            if (cards.isEmpty) {
              return Center(
                child: Text(
                  _controller.selectedCharacterTypeIds.isEmpty
                      ? '请先选择角色类型'
                      : '该类型下暂无可用的角色卡片',
                  style: TextStyle(fontSize: isSmallScreen ? 12 : 14),
                ),
              );
            }

            return ListView.builder(
              itemCount: cards.length,
              itemBuilder: (context, index) {
                final card = cards[index];
                final isSelected =
                    _controller.selectedCharacterIds.contains(card.id);

                return CheckboxListTile(
                  title: Text(
                    card.name,
                    style: TextStyle(fontSize: isSmallScreen ? 12 : 14),
                  ),
                  subtitle: Text(
                    '${card.gender} • ${card.age}',
                    style: TextStyle(fontSize: isSmallScreen ? 10 : 12),
                  ),
                  value: isSelected,
                  onChanged: (selected) {
                    _controller.toggleCharacterCard(card.id);
                  },
                  dense: true,
                  visualDensity: isSmallScreen
                      ? VisualDensity.compact
                      : VisualDensity.standard,
                );
              },
            );
          }),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              '完成',
              style: TextStyle(fontSize: isSmallScreen ? 12 : 14),
            ),
          ),
        ],
      ),
    );
  }

  /// 开始AI生成
  void _startAIGeneration() async {
    try {
      // 获取NovelAgentController来访问左侧编辑器
      late final NovelAgentController agentController;
      try {
        agentController = Get.find<NovelAgentController>();
      } catch (e) {
        Get.snackbar('错误', '无法访问编辑器，请确保在岱宗AI辅助界面中使用');
        return;
      }

      // 清空左侧编辑器内容
      if (agentController.currentChapter.value != null) {
        final currentChapter = agentController.currentChapter.value!;
        final updatedChapter = currentChapter.copyWith(content: '');
        agentController.currentChapter.value = updatedChapter;
      }

      // 开始流式生成
      await _controller.startStreamGeneration(
        onContentUpdate: (content) {
          // 更新左侧编辑器内容
          if (agentController.currentChapter.value != null) {
            final currentChapter = agentController.currentChapter.value!;
            final updatedChapter = currentChapter.copyWith(content: content);
            agentController.currentChapter.value = updatedChapter;
          }
        },
        onStatusUpdate: (status) {
          // 可以在这里显示状态更新，比如在底部显示进度
          // 暂时不显示状态，避免控制台输出
        },
        onComplete: () {
          Get.snackbar('完成', 'AI写作已完成，请查看左侧编辑器');
        },
        onError: (error) {
          Get.snackbar('错误', error);
        },
      );
    } catch (e) {
      _controller.isGenerating.value = false;
      Get.snackbar('错误', '启动AI生成失败: $e');
    }
  }

  /// 显示文风包选择对话框
  void _showStylePackageDialog() {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择文风包'),
        content: SizedBox(
          width: isSmallScreen ? screenSize.width * 0.9 : 500,
          height: isSmallScreen ? screenSize.height * 0.6 : 400,
          child: Obx(() {
            final packages = _controller.getAvailableStylePackages();
            if (packages.isEmpty) {
              return const Center(
                child: Text('暂无可用的文风包'),
              );
            }

            return ListView.builder(
              itemCount: packages.length,
              itemBuilder: (context, index) {
                final package = packages[index];

                return Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: RadioListTile<WritingStylePackage>(
                    title: Text(
                      package.name,
                      style: TextStyle(
                        fontSize: isSmallScreen ? 12 : 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (package.author.isNotEmpty)
                          Text(
                            '作者: ${package.author}',
                            style: TextStyle(
                              fontSize: isSmallScreen ? 10 : 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        const SizedBox(height: 4),
                        Text(
                          package.description,
                          style: TextStyle(
                            fontSize: isSmallScreen ? 10 : 12,
                            color: Colors.grey[700],
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                    value: package,
                    groupValue: _controller.selectedStylePackage.value,
                    onChanged: (WritingStylePackage? selected) {
                      _controller.selectStylePackage(selected);
                      Navigator.of(context).pop();
                    },
                    dense: isSmallScreen,
                  ),
                );
              },
            );
          }),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          if (_controller.selectedStylePackage.value != null)
            TextButton(
              onPressed: () {
                _controller.selectStylePackage(null);
                Navigator.of(context).pop();
              },
              child: const Text('清除选择'),
            ),
        ],
      ),
    );
  }
}
